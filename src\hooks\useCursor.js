import { useState, useEffect } from 'react'

export const useCursor = () => {
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 })
  const [isHovering, setIsHovering] = useState(false)
  const [cursorVariant, setCursorVariant] = useState('default')

  useEffect(() => {
    const updateCursorPosition = (e) => {
      setCursorPosition({ x: e.clientX, y: e.clientY })
    }

    const handleMouseEnter = (e) => {
      const target = e.target
      setIsHovering(true)
      
      // Different cursor variants based on element type
      if (target.matches('a, button, .interactive')) {
        setCursorVariant('hover')
      } else if (target.matches('.card, .card-hover')) {
        setCursorVariant('card')
      } else if (target.matches('input, textarea')) {
        setCursorVariant('text')
      } else {
        setCursorVariant('hover')
      }
    }

    const handleMouseLeave = () => {
      setIsHovering(false)
      setCursorVariant('default')
    }

    // Add event listeners
    window.addEventListener('mousemove', updateCursorPosition)
    
    // Add hover listeners to interactive elements
    const addHoverListeners = () => {
      const interactiveElements = document.querySelectorAll('a, button, .interactive, .card, .card-hover, input, textarea')
      
      interactiveElements.forEach(el => {
        el.addEventListener('mouseenter', handleMouseEnter)
        el.addEventListener('mouseleave', handleMouseLeave)
      })

      return () => {
        interactiveElements.forEach(el => {
          el.removeEventListener('mouseenter', handleMouseEnter)
          el.removeEventListener('mouseleave', handleMouseLeave)
        })
      }
    }

    // Initial setup
    const cleanup = addHoverListeners()

    // Re-setup listeners when DOM changes (for dynamic content)
    const observer = new MutationObserver(() => {
      cleanup()
      addHoverListeners()
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })

    return () => {
      window.removeEventListener('mousemove', updateCursorPosition)
      cleanup()
      observer.disconnect()
    }
  }, [])

  return {
    cursorPosition,
    isHovering,
    cursorVariant
  }
}

export const useScrollProgress = () => {
  const [scrollProgress, setScrollProgress] = useState(0)

  useEffect(() => {
    const updateScrollProgress = () => {
      const scrollTop = window.scrollY
      const docHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = docHeight > 0 ? scrollTop / docHeight : 0
      setScrollProgress(progress)
    }

    window.addEventListener('scroll', updateScrollProgress)
    updateScrollProgress() // Initial calculation

    return () => window.removeEventListener('scroll', updateScrollProgress)
  }, [])

  return scrollProgress
}

export const usePageLoad = () => {
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Wait for fonts and images to load
    window.addEventListener('load', () => {
      // Add a minimum delay for the loader
      setTimeout(() => {
        setIsLoaded(true)
      }, 2000)
    })

    // Fallback in case load event doesn't fire
    const fallbackTimer = setTimeout(() => {
      setIsLoaded(true)
    }, 3000)

    return () => {
      window.removeEventListener('load', () => {})
      clearTimeout(fallbackTimer)
    }
  }, [])

  return isLoaded
}
