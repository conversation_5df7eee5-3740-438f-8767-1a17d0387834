import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { FiUsers, FiTrendingUp, FiTarget, FiStar } from 'react-icons/fi'
import { FaRocket, FaAward } from 'react-icons/fa'

const Timeline = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.2 })

  const milestones = [
    {
      year: "2023",
      title: "The Beginning",
      description: "Script&Style was founded with a vision to democratize digital excellence and make professional web development accessible to everyone.",
      icon: FaRocket,
      color: "from-blue-500 to-cyan-500",
      side: "left"
    },
    {
      year: "2023",
      title: "First Clients",
      description: "Successfully delivered our first portfolio websites for freelancers and students, establishing our reputation for quality and creativity.",
      icon: FiUsers,
      color: "from-purple-500 to-pink-500",
      side: "right"
    },
    {
      year: "2024",
      title: "Rapid Growth",
      description: "Expanded our services to include full-stack development and UI/UX design, serving over 50 satisfied clients across various industries.",
      icon: FiTrendingUp,
      color: "from-green-500 to-emerald-500",
      side: "left"
    },
    {
      year: "2024",
      title: "Recognition",
      description: "Received recognition for innovative design solutions and became a trusted partner for startups looking to establish their digital presence.",
      icon: FaAward,
      color: "from-orange-500 to-red-500",
      side: "right"
    },
    {
      year: "2025",
      title: "Innovation Focus",
      description: "Launched our consulting services and began focusing on cutting-edge technologies to help clients stay ahead of the curve.",
      icon: FiTarget,
      color: "from-indigo-500 to-purple-500",
      side: "left"
    },
    {
      year: "Future",
      title: "Your Success",
      description: "Ready to be part of your journey and help you achieve your digital goals with our expertise and passion for excellence.",
      icon: FiStar,
      color: "from-pink-500 to-rose-500",
      side: "right"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="timeline" className="py-20 lg:py-32 bg-base-200/30 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-0 w-96 h-96 bg-secondary rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-80 h-80 bg-accent rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold font-display mb-6">
            Our <span className="gradient-text">Journey</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto mb-8" />
          <p className="text-xl text-base-content/70 max-w-3xl mx-auto">
            From a simple idea to a thriving creative tech startup - here's how we've grown and where we're heading.
          </p>
        </motion.div>

        {/* Timeline */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="relative max-w-6xl mx-auto"
        >
          {/* Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary via-secondary to-accent rounded-full" />

          {/* Timeline Items */}
          <div className="space-y-16">
            {milestones.map((milestone, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className={`flex items-center ${
                  milestone.side === 'left' 
                    ? 'lg:flex-row-reverse' 
                    : 'lg:flex-row'
                } flex-col lg:space-x-8`}
              >
                {/* Content Card */}
                <div className={`w-full lg:w-5/12 ${
                  milestone.side === 'left' ? 'lg:text-right' : 'lg:text-left'
                } text-center lg:text-left`}>
                  <motion.div
                    whileHover={{ scale: 1.02, y: -5 }}
                    className="card bg-base-100 shadow-xl border border-base-content/10 card-hover interactive group"
                  >
                    <div className="card-body p-6">
                      {/* Year Badge */}
                      <div className={`badge badge-lg ${
                        milestone.side === 'left' ? 'lg:ml-auto' : 'lg:mr-auto'
                      } mx-auto lg:mx-0 mb-4`}>
                        <span className={`bg-gradient-to-r ${milestone.color} bg-clip-text text-transparent font-bold`}>
                          {milestone.year}
                        </span>
                      </div>

                      {/* Title */}
                      <h3 className="text-2xl font-bold font-display mb-3 text-base-content">
                        {milestone.title}
                      </h3>

                      {/* Description */}
                      <p className="text-base-content/70 leading-relaxed">
                        {milestone.description}
                      </p>
                    </div>

                    {/* Hover Gradient Overlay */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${milestone.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300 rounded-2xl`} />
                  </motion.div>
                </div>

                {/* Timeline Icon */}
                <div className="relative z-10 flex-shrink-0 my-8 lg:my-0">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    className={`w-16 h-16 rounded-full bg-gradient-to-br ${milestone.color} shadow-lg flex items-center justify-center border-4 border-base-100`}
                  >
                    <milestone.icon className="text-white" size={24} />
                  </motion.div>
                  
                  {/* Connecting Lines */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div className={`w-8 h-1 bg-gradient-to-r ${milestone.color} ${
                      milestone.side === 'left' ? 'rotate-45' : '-rotate-45'
                    } hidden lg:block`} />
                  </div>
                </div>

                {/* Spacer for opposite side */}
                <div className="w-full lg:w-5/12 hidden lg:block" />
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 1.2 }}
          className="text-center mt-20"
        >
          <div className="card bg-base-100 shadow-xl border border-base-content/10 max-w-2xl mx-auto">
            <div className="card-body p-8 text-center">
              <h3 className="text-3xl font-bold font-display gradient-text mb-4">
                Ready to Write Your Success Story?
              </h3>
              <p className="text-lg text-base-content/70 mb-6">
                Join the growing list of satisfied clients who have transformed their digital presence with Script&Style.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="btn btn-primary btn-lg glow-button interactive px-8"
                onClick={() => document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Start Your Journey
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Timeline
