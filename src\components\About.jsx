import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { FiUsers, FiTrendingUp, FiHeart, FiTarget } from 'react-icons/fi'

const About = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.3 })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const stats = [
    { icon: FiUsers, number: "50+", label: "Happy Clients" },
    { icon: FiTrendingUp, number: "100+", label: "Projects Completed" },
    { icon: FiHeart, number: "99%", label: "Client Satisfaction" },
    { icon: FiTarget, number: "24/7", label: "Support Available" }
  ]

  return (
    <section id="about" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-0 w-72 h-72 bg-primary rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-secondary rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center"
        >
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <motion.div variants={itemVariants}>
              <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold font-display mb-6">
                About{' '}
                <span className="gradient-text">Script&Style</span>
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary rounded-full mb-8" />
            </motion.div>

            <motion.p
              variants={itemVariants}
              className="text-lg sm:text-xl text-base-content/80 leading-relaxed"
            >
              We're not just another development agency. We're storytellers, 
              problem-solvers, and digital craftspeople who believe that great 
              design and clean code can change the world.
            </motion.p>

            <motion.p
              variants={itemVariants}
              className="text-lg text-base-content/70 leading-relaxed"
            >
              Founded with a passion for bridging the gap between creativity and 
              technology, Script&Style empowers students, freelancers, and startups 
              to bring their digital visions to life. From sleek portfolios to 
              complex web applications, we craft experiences that resonate.
            </motion.p>

            <motion.div
              variants={itemVariants}
              className="flex flex-wrap gap-4"
            >
              <div className="badge badge-primary badge-lg">Modern Design</div>
              <div className="badge badge-secondary badge-lg">Clean Code</div>
              <div className="badge badge-accent badge-lg">User-Focused</div>
              <div className="badge badge-info badge-lg">Performance</div>
            </motion.div>

            <motion.button
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn btn-primary glow-button interactive"
              onClick={() => document.querySelector('#services')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Discover Our Services
            </motion.button>
          </div>

          {/* Right Column - Stats and Visual */}
          <div className="space-y-8">
            <motion.div
              variants={itemVariants}
              className="relative"
            >
              {/* Main Visual Card */}
              <div className="glass-card shadow-xl p-8 relative overflow-hidden">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold font-display gradient-text mb-4">
                    Our Mission
                  </h3>
                  <p className="text-base-content/70">
                    To democratize digital excellence by making professional 
                    web development and design accessible to everyone.
                  </p>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-2 gap-6">
                  {stats.map((stat, index) => (
                    <motion.div
                      key={stat.label}
                      variants={itemVariants}
                      className="text-center p-4 rounded-lg glass interactive-scale"
                      whileHover={{ scale: 1.05 }}
                    >
                      <div className="flex justify-center mb-3">
                        <div className="p-3 rounded-full bg-primary/10">
                          <stat.icon className="text-primary" size={24} />
                        </div>
                      </div>
                      <div className="text-2xl font-bold gradient-text mb-1">
                        {stat.number}
                      </div>
                      <div className="text-sm text-base-content/70">
                        {stat.label}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Floating Elements */}
              <motion.div
                animate={{ 
                  y: [0, -10, 0],
                  rotate: [0, 2, 0]
                }}
                transition={{ 
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-full opacity-20"
              />
              
              <motion.div
                animate={{ 
                  y: [0, 10, 0],
                  rotate: [0, -2, 0]
                }}
                transition={{ 
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
                className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-accent to-secondary rounded-full opacity-20"
              />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
