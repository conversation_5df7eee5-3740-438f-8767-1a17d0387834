# Script&Style - Modern Animated Landing Page

A highly interactive, animated landing page for Script&Style, a creative tech startup specializing in custom web & app development, UI/UX design, digital portfolios, and consulting.

## 🚀 Features

### ✨ Animations & Interactions
- **Custom Animated Cursor** - Changes size and style based on hover targets (desktop only)
- **Scroll-triggered Animations** - Elements animate into view using Framer Motion
- **Floating Background Elements** - Subtle animated shapes that enhance the visual experience
- **Smooth Scroll Progress Indicator** - Shows reading progress at the top of the page
- **Page Load Animation** - Elegant loading screen with animated logo
- **Hover Effects** - Interactive cards, buttons, and elements with smooth transitions

### 🎨 Design & UI
- **Modern Design System** - Clean, professional aesthetic with gradient accents
- **Responsive Layout** - Optimized for mobile, tablet, and desktop devices
- **Dark/Light Theme Support** - Built with DaisyUI theme system
- **Typography** - Custom font stack with Inter, Poppins, and JetBrains Mono
- **Color Palette** - Carefully chosen primary, secondary, and accent colors

### 🔧 Technical Features
- **Performance Optimized** - Lazy loading images, optimized animations
- **Accessibility** - Respects reduced motion preferences
- **Mobile-First** - Responsive design with mobile-specific optimizations
- **SEO Ready** - Semantic HTML structure and meta tags
- **Modern Stack** - Built with latest React, Vite, and Tailwind CSS

## 🛠️ Tech Stack

- **React.js** - Component-based architecture
- **Vite** - Fast development and build tool
- **TailwindCSS** - Utility-first CSS framework
- **DaisyUI** - Accessible component library
- **Framer Motion** - Animation library for React
- **React Icons** - Icon library (Feather Icons + Font Awesome)

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **Open in browser**
   Navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

## 🎯 Key Sections

- **Hero** - Animated brand name with typewriter effect
- **About** - Company story with animated statistics
- **Services** - Interactive service cards with hover effects
- **Portfolio** - Project showcase with modal functionality
- **Timeline** - Company milestones with scroll animations
- **Contact** - Interactive form with validation
- **Footer** - Social links and back-to-top functionality

## 📱 Responsive Design

Fully responsive with mobile-first approach. Custom cursor disabled on mobile for optimal performance.

## ⚡ Performance Features

- Lazy loading images
- Optimized animations with reduced motion support
- Code splitting and tree-shaking
- Compressed assets

---

**Built with ❤️ by Script&Style**
