{"name": "script-and-style", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.17", "daisyui": "^4.6.1", "framer-motion": "^11.0.3", "postcss": "^8.4.33", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "tailwindcss": "^3.4.1"}, "devDependencies": {"@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.12"}}