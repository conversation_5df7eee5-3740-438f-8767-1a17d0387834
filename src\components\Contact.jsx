import { useState } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { FiMail, FiPhone, FiMapPin, FiSend, FiLinkedin, FiGithub, FiInstagram, FiTwitter } from 'react-icons/fi'

const Contact = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.2 })
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState(null)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      setSubmitStatus('success')
      setFormData({ name: '', email: '', subject: '', message: '' })
      
      // Reset status after 3 seconds
      setTimeout(() => setSubmitStatus(null), 3000)
    }, 2000)
  }

  const contactInfo = [
    {
      icon: FiMail,
      title: "Email Us",
      content: "<EMAIL>",
      link: "mailto:<EMAIL>"
    },
    {
      icon: FiPhone,
      title: "Call Us",
      content: "+****************",
      link: "tel:+15551234567"
    },
    {
      icon: FiMapPin,
      title: "Visit Us",
      content: "123 Creative Street, Tech City, TC 12345",
      link: "#"
    }
  ]

  const socialLinks = [
    { icon: FiLinkedin, url: "#", color: "hover:text-blue-600" },
    { icon: FiGithub, url: "#", color: "hover:text-gray-800" },
    { icon: FiInstagram, url: "#", color: "hover:text-pink-600" },
    { icon: FiTwitter, url: "#", color: "hover:text-blue-400" }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="contact" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 right-1/4 w-96 h-96 bg-primary rounded-full blur-3xl" />
        <div className="absolute bottom-0 left-1/4 w-80 h-80 bg-secondary rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold font-display mb-6">
            Get In <span className="gradient-text">Touch</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto mb-8" />
          <p className="text-xl text-base-content/70 max-w-3xl mx-auto">
            Ready to bring your vision to life? Let's discuss your project and create something amazing together.
          </p>
        </motion.div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid lg:grid-cols-2 gap-12 lg:gap-20"
        >
          {/* Contact Form */}
          <motion.div variants={itemVariants}>
            <div className="glass-card shadow-xl relative overflow-hidden">
              <div className="card-body p-8">
                <h3 className="text-2xl font-bold font-display mb-6 gradient-text">
                  Send us a message
                </h3>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Name Input */}
                  <motion.div
                    whileFocus={{ scale: 1.02 }}
                    className="form-control"
                  >
                    <label className="label">
                      <span className="label-text font-medium">Name</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Your full name"
                      className="input input-bordered w-full focus:input-primary transition-all duration-300"
                      required
                    />
                  </motion.div>

                  {/* Email Input */}
                  <motion.div
                    whileFocus={{ scale: 1.02 }}
                    className="form-control"
                  >
                    <label className="label">
                      <span className="label-text font-medium">Email</span>
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className="input input-bordered w-full focus:input-primary transition-all duration-300"
                      required
                    />
                  </motion.div>

                  {/* Subject Input */}
                  <motion.div
                    whileFocus={{ scale: 1.02 }}
                    className="form-control"
                  >
                    <label className="label">
                      <span className="label-text font-medium">Subject</span>
                    </label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="What's this about?"
                      className="input input-bordered w-full focus:input-primary transition-all duration-300"
                      required
                    />
                  </motion.div>

                  {/* Message Textarea */}
                  <motion.div
                    whileFocus={{ scale: 1.02 }}
                    className="form-control"
                  >
                    <label className="label">
                      <span className="label-text font-medium">Message</span>
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Tell us about your project..."
                      className="textarea textarea-bordered h-32 w-full focus:textarea-primary transition-all duration-300 resize-none"
                      required
                    />
                  </motion.div>

                  {/* Submit Button */}
                  <motion.button
                    type="submit"
                    disabled={isSubmitting}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`btn btn-primary w-full glow-button interactive ${
                      isSubmitting ? 'loading' : ''
                    }`}
                  >
                    {isSubmitting ? (
                      'Sending...'
                    ) : (
                      <>
                        <FiSend className="mr-2" />
                        Send Message
                      </>
                    )}
                  </motion.button>

                  {/* Success Message */}
                  {submitStatus === 'success' && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="alert alert-success"
                    >
                      <span>Message sent successfully! We'll get back to you soon.</span>
                    </motion.div>
                  )}
                </form>
              </div>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div variants={itemVariants} className="space-y-8">
            {/* Contact Info Cards */}
            <div className="space-y-6">
              {contactInfo.map((info, index) => (
                <motion.a
                  key={info.title}
                  href={info.link}
                  whileHover={{ scale: 1.02, x: 10 }}
                  className="glass-card shadow-lg card-hover interactive group block"
                >
                  <div className="card-body p-6 flex flex-row items-center">
                    <div className="p-3 rounded-full bg-primary/10 group-hover:bg-primary/20 transition-colors duration-300 mr-4">
                      <info.icon className="text-primary" size={24} />
                    </div>
                    <div>
                      <h4 className="font-bold text-lg mb-1">{info.title}</h4>
                      <p className="text-base-content/70">{info.content}</p>
                    </div>
                  </div>
                </motion.a>
              ))}
            </div>

            {/* Social Links */}
            <div className="glass-card shadow-xl relative overflow-hidden">
              <div className="card-body p-8 text-center">
                <h3 className="text-2xl font-bold font-display mb-6 gradient-text">
                  Follow Us
                </h3>
                <p className="text-base-content/70 mb-6">
                  Stay connected and follow our journey on social media
                </p>
                <div className="flex justify-center space-x-6">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={index}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.2, y: -5 }}
                      whileTap={{ scale: 0.9 }}
                      className={`p-3 rounded-full bg-base-200 hover:bg-primary hover:text-white transition-all duration-300 interactive ${social.color}`}
                    >
                      <social.icon size={24} />
                    </motion.a>
                  ))}
                </div>
              </div>
            </div>

            {/* Quick Response Promise */}
            <div className="card bg-gradient-to-br from-primary/10 to-secondary/10 border border-primary/20">
              <div className="card-body p-6 text-center">
                <h4 className="font-bold text-lg mb-2 gradient-text">
                  Quick Response Guarantee
                </h4>
                <p className="text-base-content/70 text-sm">
                  We typically respond to all inquiries within 24 hours. 
                  For urgent matters, don't hesitate to call us directly.
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Contact
