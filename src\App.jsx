import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import Navbar from './components/Navbar'
import Hero from './components/Hero'
import About from './components/About'
import Services from './components/Services'
import Portfolio from './components/Portfolio'
import Timeline from './components/Timeline'
import Contact from './components/Contact'
import Footer from './components/Footer'
import CustomCursor from './components/CustomCursor'
import ScrollProgress from './components/ScrollProgress'
import PageLoader from './components/PageLoader'
import { useResponsive } from './hooks/useResponsive'

function App() {
  const { isMobile } = useResponsive()

  // Hide default cursor on desktop only
  useEffect(() => {
    if (!isMobile) {
      document.body.style.cursor = 'none'
    }
    return () => {
      document.body.style.cursor = 'auto'
    }
  }, [isMobile])

  return (
    <div className="relative min-h-screen">
      {/* Page Loader */}
      <PageLoader />

      {/* Custom Cursor */}
      <CustomCursor />

      {/* Scroll Progress Indicator */}
      <ScrollProgress />

      {/* Floating Background Shapes */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="floating-shape w-64 h-64 bg-primary rounded-full absolute top-1/4 left-1/4 opacity-10"
          animate={{
            y: [0, -20, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="floating-shape w-48 h-48 bg-secondary rounded-full absolute top-3/4 right-1/4 opacity-10"
          animate={{
            y: [0, 20, 0],
            rotate: [0, -180, -360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="floating-shape w-32 h-32 bg-accent rounded-full absolute top-1/2 right-1/3 opacity-10"
          animate={{
            y: [0, -15, 0],
            x: [0, 15, 0],
            rotate: [0, 90, 180, 270, 360],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      {/* Main Content */}
      <main className="relative z-10">
        <Navbar />
        <Hero />
        <About />
        <Services />
        <Portfolio />
        <Timeline />
        <Contact />
        <Footer />
      </main>
    </div>
  )
}

export default App
