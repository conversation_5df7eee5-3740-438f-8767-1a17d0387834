import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef, useState } from 'react'
import { FiExternalLink, FiGithub, FiEye } from 'react-icons/fi'
import LazyImage from './LazyImage'

const Portfolio = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.2 })
  const [selectedProject, setSelectedProject] = useState(null)

  const projects = [
    {
      id: 1,
      title: "EcoTech Startup",
      category: "Web Development",
      description: "A modern, responsive website for a sustainable technology startup featuring interactive animations and clean design.",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop",
      technologies: ["React", "Tailwind CSS", "Framer Motion", "Node.js"],
      liveUrl: "#",
      githubUrl: "#",
      color: "from-green-500 to-emerald-500"
    },
    {
      id: 2,
      title: "Creative Portfolio",
      category: "UI/UX Design",
      description: "A stunning portfolio website for a digital artist showcasing their work with smooth transitions and modern aesthetics.",
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&h=400&fit=crop",
      technologies: ["Figma", "React", "GSAP", "CSS Grid"],
      liveUrl: "#",
      githubUrl: "#",
      color: "from-purple-500 to-pink-500"
    },
    {
      id: 3,
      title: "FinTech Dashboard",
      category: "Web Application",
      description: "A comprehensive financial dashboard with real-time data visualization and intuitive user interface.",
      image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
      technologies: ["React", "D3.js", "TypeScript", "Express"],
      liveUrl: "#",
      githubUrl: "#",
      color: "from-blue-500 to-cyan-500"
    },
    {
      id: 4,
      title: "Food Delivery App",
      category: "Mobile Design",
      description: "A sleek mobile app design for food delivery service with focus on user experience and accessibility.",
      image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=600&h=400&fit=crop",
      technologies: ["React Native", "Firebase", "Stripe", "Maps API"],
      liveUrl: "#",
      githubUrl: "#",
      color: "from-orange-500 to-red-500"
    },
    {
      id: 5,
      title: "Learning Platform",
      category: "EdTech",
      description: "An interactive online learning platform with video streaming, progress tracking, and community features.",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop",
      technologies: ["Next.js", "Prisma", "PostgreSQL", "WebRTC"],
      liveUrl: "#",
      githubUrl: "#",
      color: "from-indigo-500 to-purple-500"
    },
    {
      id: 6,
      title: "E-commerce Store",
      category: "Full Stack",
      description: "A complete e-commerce solution with payment processing, inventory management, and admin dashboard.",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      liveUrl: "#",
      githubUrl: "#",
      color: "from-teal-500 to-green-500"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <section id="portfolio" className="py-20 lg:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/3 right-0 w-96 h-96 bg-accent rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 left-0 w-80 h-80 bg-primary rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold font-display mb-6">
            Featured <span className="gradient-text">Work</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto mb-8" />
          <p className="text-xl text-base-content/70 max-w-3xl mx-auto">
            Explore our latest projects and see how we bring ideas to life through code and design.
          </p>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              variants={cardVariants}
              whileHover={{ y: -10 }}
              className="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 border border-base-content/10 card-hover interactive group overflow-hidden"
            >
              {/* Project Image */}
              <figure className="relative overflow-hidden h-48">
                <LazyImage
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                  <div className="flex space-x-4">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="btn btn-circle btn-primary interactive"
                      onClick={() => setSelectedProject(project)}
                    >
                      <FiEye size={20} />
                    </motion.button>
                    <motion.a
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="btn btn-circle btn-secondary interactive"
                    >
                      <FiExternalLink size={20} />
                    </motion.a>
                    <motion.a
                      href={project.githubUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="btn btn-circle btn-accent interactive"
                    >
                      <FiGithub size={20} />
                    </motion.a>
                  </div>
                </div>
              </figure>

              <div className="card-body p-6">
                {/* Category Badge */}
                <div className="badge badge-primary badge-sm mb-2">{project.category}</div>
                
                {/* Title */}
                <h3 className="card-title text-xl font-bold font-display mb-3">
                  {project.title}
                </h3>

                {/* Description */}
                <p className="text-base-content/70 text-sm leading-relaxed mb-4">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={tech}
                      className="badge badge-outline badge-xs"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="card-actions justify-end">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="btn btn-primary btn-sm glow-button interactive"
                    onClick={() => setSelectedProject(project)}
                  >
                    View Details
                  </motion.button>
                </div>
              </div>

              {/* Hover Gradient Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-br ${project.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300`} />
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="text-center mt-16"
        >
          <p className="text-lg text-base-content/70 mb-8">
            Like what you see? Let's create something amazing together.
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="btn btn-primary btn-lg glow-button interactive px-8"
            onClick={() => document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' })}
          >
            Start Your Project
          </motion.button>
        </motion.div>
      </div>

      {/* Project Modal */}
      {selectedProject && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedProject(null)}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            className="bg-base-100 rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-start mb-6">
              <h3 className="text-3xl font-bold font-display gradient-text">
                {selectedProject.title}
              </h3>
              <button
                className="btn btn-circle btn-sm"
                onClick={() => setSelectedProject(null)}
              >
                ✕
              </button>
            </div>
            
            <LazyImage
              src={selectedProject.image}
              alt={selectedProject.title}
              className="w-full h-64 rounded-lg mb-6"
            />
            
            <p className="text-base-content/80 mb-6 leading-relaxed">
              {selectedProject.description}
            </p>
            
            <div className="flex flex-wrap gap-2 mb-6">
              {selectedProject.technologies.map((tech) => (
                <span key={tech} className="badge badge-primary">
                  {tech}
                </span>
              ))}
            </div>
            
            <div className="flex gap-4">
              <a
                href={selectedProject.liveUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary flex-1"
              >
                <FiExternalLink className="mr-2" />
                Live Demo
              </a>
              <a
                href={selectedProject.githubUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-outline flex-1"
              >
                <FiGithub className="mr-2" />
                Source Code
              </a>
            </div>
          </motion.div>
        </motion.div>
      )}
    </section>
  )
}

export default Portfolio
