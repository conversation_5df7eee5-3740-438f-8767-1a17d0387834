import { motion } from 'framer-motion'
import { <PERSON>ArrowUp, FiHeart, FiLinkedin, FiGithub, FiInstagram, FiTwitter, FiMail, FiPhone } from 'react-icons/fi'

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const quickLinks = [
    { name: 'Home', href: '#home' },
    { name: 'About', href: '#about' },
    { name: 'Services', href: '#services' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'Timeline', href: '#timeline' },
    { name: 'Contact', href: '#contact' }
  ]

  const services = [
    'Web Development',
    'UI/UX Design',
    'Portfolio Sites',
    'Consulting',
    'Mobile Apps',
    'E-commerce'
  ]

  const socialLinks = [
    { icon: FiLinkedin, url: "#", label: "LinkedIn" },
    { icon: FiGith<PERSON>, url: "#", label: "GitHub" },
    { icon: <PERSON>Instagram, url: "#", label: "Instagram" },
    { icon: FiTwitter, url: "#", label: "Twitter" }
  ]

  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <footer className="bg-base-200 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary rounded-full blur-3xl" />
        <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-secondary rounded-full blur-3xl" />
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="py-16">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h3 className="text-3xl font-bold font-display gradient-text mb-4">
                  Script&Style
                </h3>
                <p className="text-base-content/70 mb-6 leading-relaxed">
                  Where design meets code. We craft digital stories that resonate, 
                  inspire, and drive results for students, freelancers, and startups.
                </p>
                <div className="flex space-x-4">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={social.label}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.2, y: -3 }}
                      whileTap={{ scale: 0.9 }}
                      className="p-2 rounded-full bg-base-100 hover:bg-primary hover:text-white transition-all duration-300 interactive"
                      aria-label={social.label}
                    >
                      <social.icon size={20} />
                    </motion.a>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Quick Links */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <h4 className="text-lg font-bold font-display mb-4 text-base-content">
                  Quick Links
                </h4>
                <ul className="space-y-3">
                  {quickLinks.map((link, index) => (
                    <li key={link.name}>
                      <motion.a
                        href={link.href}
                        onClick={(e) => {
                          e.preventDefault()
                          scrollToSection(link.href)
                        }}
                        whileHover={{ x: 5 }}
                        className="text-base-content/70 hover:text-primary transition-colors duration-200 interactive"
                      >
                        {link.name}
                      </motion.a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Services */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h4 className="text-lg font-bold font-display mb-4 text-base-content">
                  Services
                </h4>
                <ul className="space-y-3">
                  {services.map((service, index) => (
                    <li key={service}>
                      <motion.span
                        whileHover={{ x: 5 }}
                        className="text-base-content/70 hover:text-primary transition-colors duration-200 cursor-pointer interactive"
                        onClick={() => scrollToSection('#services')}
                      >
                        {service}
                      </motion.span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>

            {/* Contact Info */}
            <div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <h4 className="text-lg font-bold font-display mb-4 text-base-content">
                  Get In Touch
                </h4>
                <div className="space-y-3">
                  <motion.a
                    href="mailto:<EMAIL>"
                    whileHover={{ x: 5 }}
                    className="flex items-center text-base-content/70 hover:text-primary transition-colors duration-200 interactive"
                  >
                    <FiMail className="mr-3" size={16} />
                    <EMAIL>
                  </motion.a>
                  <motion.a
                    href="tel:+15551234567"
                    whileHover={{ x: 5 }}
                    className="flex items-center text-base-content/70 hover:text-primary transition-colors duration-200 interactive"
                  >
                    <FiPhone className="mr-3" size={16} />
                    +****************
                  </motion.a>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="btn btn-primary btn-sm glow-button interactive mt-4"
                    onClick={() => scrollToSection('#contact')}
                  >
                    Start Your Project
                  </motion.button>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-base-content/10 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-base-content/60 text-sm mb-4 md:mb-0 flex items-center"
            >
              © 2025 Script&Style. Made with{' '}
              <motion.span
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="mx-1 text-red-500"
              >
                <FiHeart size={14} />
              </motion.span>
              for creators and innovators.
            </motion.p>

            <div className="flex items-center space-x-6">
              <motion.a
                href="#"
                whileHover={{ scale: 1.05 }}
                className="text-base-content/60 hover:text-primary text-sm transition-colors duration-200 interactive"
              >
                Privacy Policy
              </motion.a>
              <motion.a
                href="#"
                whileHover={{ scale: 1.05 }}
                className="text-base-content/60 hover:text-primary text-sm transition-colors duration-200 interactive"
              >
                Terms of Service
              </motion.a>
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      <motion.button
        onClick={scrollToTop}
        whileHover={{ scale: 1.1, y: -2 }}
        whileTap={{ scale: 0.9 }}
        className="fixed bottom-8 right-8 btn btn-circle btn-primary shadow-lg glow-button interactive z-40"
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 1 }}
      >
        <FiArrowUp size={20} />
      </motion.button>
    </footer>
  )
}

export default Footer
