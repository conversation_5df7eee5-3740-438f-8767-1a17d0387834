import { motion } from 'framer-motion'
import { useCursor } from '../hooks/useCursor'
import { useResponsive } from '../hooks/useResponsive'

const CustomCursor = () => {
  const { cursorPosition, isHovering, cursorVariant } = useCursor()
  const { isMobile } = useResponsive()

  // Don't render cursor on mobile devices
  if (isMobile) return null

  const cursorVariants = {
    default: {
      width: 20,
      height: 20,
      backgroundColor: 'rgba(14, 165, 233, 0.8)',
      mixBlendMode: 'difference',
      border: 'none',
    },
    hover: {
      width: 40,
      height: 40,
      backgroundColor: 'rgba(217, 70, 239, 0.6)',
      mixBlendMode: 'difference',
      border: '2px solid rgba(217, 70, 239, 0.8)',
    },
    card: {
      width: 60,
      height: 60,
      backgroundColor: 'rgba(249, 115, 22, 0.4)',
      mixBlendMode: 'difference',
      border: '2px solid rgba(249, 115, 22, 0.6)',
    },
    text: {
      width: 2,
      height: 24,
      backgroundColor: 'rgba(14, 165, 233, 1)',
      mixBlendMode: 'difference',
      border: 'none',
      borderRadius: '1px',
    }
  }

  return (
    <>
      {/* Main cursor */}
      <motion.div
        className="fixed pointer-events-none z-[9999] rounded-full"
        animate={{
          x: cursorPosition.x - (cursorVariants[cursorVariant]?.width || 20) / 2,
          y: cursorPosition.y - (cursorVariants[cursorVariant]?.height || 20) / 2,
          ...cursorVariants[cursorVariant]
        }}
        transition={{
          type: "spring",
          stiffness: 500,
          damping: 28,
        }}
        style={{
          backdropFilter: 'blur(10px)',
        }}
      />
      
      {/* Cursor trail */}
      <motion.div
        className="fixed pointer-events-none z-[9998] rounded-full"
        animate={{
          x: cursorPosition.x - 4,
          y: cursorPosition.y - 4,
          scale: isHovering ? 1.5 : 1,
          opacity: isHovering ? 0.3 : 0.6,
        }}
        transition={{
          type: "spring",
          stiffness: 150,
          damping: 15,
        }}
        style={{
          width: 8,
          height: 8,
          backgroundColor: 'rgba(14, 165, 233, 0.4)',
          mixBlendMode: 'difference',
        }}
      />
    </>
  )
}

export default CustomCursor
