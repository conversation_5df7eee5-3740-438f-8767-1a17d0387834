@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for animations and effects */
@layer base {
  * {
    box-sizing: border-box;
    border-color: hsl(var(--bc) / 0.2);
  }

  html {
    scroll-behavior: smooth;
    font-size: 16px;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background: hsl(var(--b1));
    color: hsl(var(--bc));
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  @media (min-width: 768px) {
    body {
      cursor: none;
    }

    * {
      cursor: none !important;
    }
  }

  /* Improved focus styles */
  *:focus {
    outline: 2px solid hsl(var(--p));
    outline-offset: 2px;
  }

  /* Better selection colors */
  ::selection {
    background: hsl(var(--p) / 0.2);
    color: hsl(var(--pc));
  }
}

@layer components {
  /* Custom cursor styles */
  .custom-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: rgba(14, 165, 233, 0.8);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: all 0.1s ease;
    mix-blend-mode: difference;
  }

  .custom-cursor.hover {
    width: 40px;
    height: 40px;
    background: rgba(217, 70, 239, 0.6);
    backdrop-filter: blur(10px);
  }

  /* Floating background elements */
  .floating-shape {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
  }

  .floating-shape:nth-child(odd) {
    animation-delay: -2s;
  }

  .floating-shape:nth-child(even) {
    animation-delay: -4s;
  }

  /* Gradient text effect */
  .gradient-text {
    background: linear-gradient(90deg, hsl(var(--p)), hsl(var(--s)), hsl(var(--a)));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  /* Transparent gradient text effect */
  .gradient-text-transparent {
    background: linear-gradient(
      90deg,
      hsl(var(--p) / 0.8),
      hsl(var(--s) / 0.6),
      hsl(var(--a) / 0.4),
      transparent,
      hsl(var(--a) / 0.4),
      hsl(var(--s) / 0.6),
      hsl(var(--p) / 0.8)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    background-size: 200% 100%;
    animation: gradientShift 4s ease-in-out infinite;
    text-shadow: 0 0 30px hsl(var(--p) / 0.3);
  }

  /* Enhanced button effects */
  .glow-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px 0 hsl(var(--p) / 0.3);
  }

  .glow-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glow-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 hsl(var(--p) / 0.4);
  }

  .glow-button:hover::before {
    left: 100%;
  }

  .glow-button:active {
    transform: translateY(0);
  }

  /* Enhanced card hover effects */
  .card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid hsl(var(--bc) / 0.1);
    backdrop-filter: blur(10px);
  }

  .card-hover:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow:
      0 25px 50px hsl(var(--bc) / 0.15),
      0 0 0 1px hsl(var(--p) / 0.1);
    border-color: hsl(var(--p) / 0.3);
  }

  .card-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, hsl(var(--p) / 0.05), hsl(var(--s) / 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: inherit;
  }

  .card-hover:hover::before {
    opacity: 1;
  }

  /* Scroll indicator */
  .scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #0ea5e9, #d946ef, #f97316);
    transform-origin: left;
    z-index: 1000;
  }

  /* Timeline connector */
  .timeline-connector {
    position: relative;
  }

  .timeline-connector::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, #0ea5e9, #d946ef);
    transform: translateX(-50%);
  }

  /* Enhanced animated background */
  .animated-bg {
    background: linear-gradient(-45deg,
      hsl(var(--p)),
      hsl(var(--s)),
      hsl(var(--a)),
      hsl(var(--in))
    );
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Text reveal animation */
  .text-reveal {
    overflow: hidden;
  }

  .text-reveal span {
    display: inline-block;
    transform: translateY(100%);
    transition: transform 0.6s cubic-bezier(0.77, 0, 0.175, 1);
  }

  .text-reveal.animate span {
    transform: translateY(0);
  }

  /* Enhanced animations */
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .slide-down {
    animation: slideDown 0.5s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  /* Enhanced hover effects */
  .hover-lift {
    transition: transform 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }

  .hover-glow {
    transition: box-shadow 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px hsl(var(--p) / 0.3);
  }

  /* Enhanced text effects */
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--p)), hsl(var(--s)));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .text-glow {
    text-shadow: 0 0 10px hsl(var(--p) / 0.5);
  }

  /* Enhanced glass effects */
  .glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  }

  .glass-card-hover:hover {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px);
    border-color: rgba(255, 255, 255, 0.3);
  }

  /* Animation keyframes */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes glow {
    0% {
      box-shadow: 0 0 5px hsl(var(--p) / 0.3);
    }
    100% {
      box-shadow: 0 0 20px hsl(var(--p) / 0.6);
    }
  }

  @keyframes gradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
}

@layer utilities {
  /* Enhanced text shadows */
  .text-shadow {
    text-shadow: 2px 2px 4px hsl(var(--bc) / 0.1);
  }

  .text-shadow-lg {
    text-shadow: 4px 4px 8px hsl(var(--bc) / 0.2);
  }

  .text-shadow-glow {
    text-shadow: 0 0 20px hsl(var(--p) / 0.5);
  }

  /* Glassmorphism effects */
  .glass {
    background: hsl(var(--b1) / 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--bc) / 0.1);
  }

  .glass-card {
    background: linear-gradient(135deg, hsl(var(--b1) / 0.9), hsl(var(--b2) / 0.8));
    backdrop-filter: blur(20px);
    border: 1px solid hsl(var(--bc) / 0.1);
    box-shadow: 0 8px 32px hsl(var(--bc) / 0.1);
  }

  /* Enhanced backdrop blur */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .backdrop-blur-strong {
    backdrop-filter: blur(40px) saturate(180%);
  }

  /* 3D transforms */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  /* Smooth scrolling */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Custom gradients */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--p)), hsl(var(--s)));
  }

  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--s)), hsl(var(--a)));
  }

  /* Interactive elements */
  .interactive-scale {
    transition: transform 0.2s ease;
  }

  .interactive-scale:hover {
    transform: scale(1.05);
  }

  .interactive-scale:active {
    transform: scale(0.98);
  }
}
